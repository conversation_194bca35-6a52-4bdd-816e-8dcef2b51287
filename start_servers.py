#!/usr/bin/env python3
"""
Startup script to run both backend and frontend servers
"""

import subprocess
import sys
import os
import time
import signal
from pathlib import Path

def run_backend():
    """Start the FastAPI backend server"""
    backend_dir = Path("TRADER/python_backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return None
    
    print("🚀 Starting backend server...")
    return subprocess.Popen(
        [sys.executable, "server.py"],
        cwd=backend_dir,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )

def run_frontend():
    """Start the React frontend development server"""
    frontend_dir = Path("TRADER")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        return None
    
    print("🎨 Starting frontend server...")
    return subprocess.Popen(
        ["npm", "run", "dev"],
        cwd=frontend_dir,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )

def main():
    print("🚀 Advanced Trading Dashboard Startup Script")
    print("=" * 50)
    
    # Check if required directories exist
    if not Path("TRADER").exists():
        print("❌ TRADER directory not found!")
        sys.exit(1)
    
    processes = []
    
    try:
        # Start backend
        backend_process = run_backend()
        if backend_process:
            processes.append(backend_process)
            print("✅ Backend server started on http://localhost:8000")
        
        # Wait a moment for backend to start
        time.sleep(2)
        
        # Start frontend
        frontend_process = run_frontend()
        if frontend_process:
            processes.append(frontend_process)
            print("✅ Frontend server starting on http://localhost:5173")
        
        print("\n🎉 Both servers are starting up!")
        print("📱 Open http://localhost:5173 in your browser")
        print("🔧 Backend API available at http://localhost:8000")
        print("\n⚠️  Press Ctrl+C to stop both servers")
        
        # Keep the script running
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down servers...")
        for process in processes:
            process.terminate()
        
        # Wait for processes to terminate
        for process in processes:
            process.wait()
        
        print("✅ All servers stopped successfully!")

if __name__ == "__main__":
    main()
