# Trading Chatbot

A comprehensive trading chatbot application that integrates with Zerodha Kite API to provide portfolio analysis and AI-powered insights.

## Features

1. **Secure Authentication** - Password-protected access (hardcoded: "********")
2. **Kite Connect Integration** - Seamless connection to your Zerodha trading account
3. **Portfolio Display** - Real-time holdings with P&L calculations
4. **AI Chat Assistant** - OpenAI-powered chatbot for portfolio analysis and insights
5. **Responsive Design** - Modern UI built with React and Tailwind CSS

## Architecture

### Backend (FastAPI)
- **Authentication endpoint** - `/login` - Password validation
- **Kite integration** - `/kite-login-url`, `/kite-connect` - Handle Kite OAuth flow
- **Portfolio data** - `/holdings` - Fetch and display portfolio holdings
- **AI chat** - `/chat` - Process user questions using OpenAI

### Frontend (React)
- **LoginPage** - Password authentication
- **ConnectPage** - Kite Connect integration
- **Dashboard** - Portfolio display and chat interface
- **Responsive design** - Tailwind CSS styling

## Setup Instructions

### Prerequisites
- Python 3.8+
- Node.js 16+
- Zerodha Kite API credentials
- OpenAI API key

### Installation

1. **Install Python dependencies:**
   ```bash
   pip install fastapi uvicorn kiteconnect instructor openai pydantic
   ```

2. **Install Node.js dependencies:**
   ```bash
   cd TRADER
   npm install
   ```

3. **Configure API credentials:**
   - Update `server.py` with your Kite API credentials
   - Update `server.py` with your OpenAI API key

### Running the Application

#### Option 1: Use the startup script
```bash
python start_servers.py
```

#### Option 2: Run servers separately

**Terminal 1 - Backend:**
```bash
python server.py
```

**Terminal 2 - Frontend:**
```bash
cd TRADER
npm run dev
```

## Usage Flow

1. **Login** - Enter password "********"
2. **Connect to Kite** - Click "Connect to Kite" and login to Zerodha
3. **Extract Token** - Copy the request token from the redirect URL
4. **Complete Connection** - Paste the token to fetch your holdings
5. **Explore Dashboard** - View portfolio and chat with AI assistant

## API Endpoints

- `POST /login` - Authenticate with password
- `GET /kite-login-url` - Get Kite Connect login URL
- `GET /kite-token` - Handle Kite redirect (for future enhancement)
- `POST /kite-connect` - Exchange request token for access token
- `GET /holdings` - Get current portfolio holdings
- `POST /chat` - Chat with AI about your portfolio

## Security Notes

- Password is hardcoded for demo purposes
- API keys are embedded in code (use environment variables in production)
- CORS is open for development (restrict in production)

## Technologies Used

- **Backend:** FastAPI, KiteConnect, OpenAI, Pydantic
- **Frontend:** React, React Router, Axios, Tailwind CSS
- **Build Tools:** Vite, ESLint

## Future Enhancements

- Environment variable configuration
- Database integration for user sessions
- Advanced portfolio analytics
- Real-time market data
- Trading capabilities through the chatbot
- Enhanced security and authentication
