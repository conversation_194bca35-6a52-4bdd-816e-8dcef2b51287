import asyncio
from agents.mcp import MCPServerStdio

async def main():
    fetch_params = {
        "command": "npx",
        "args": ["mcp-remote", "https://mcp.kite.trade/sse"],
        "env": {
        "KITE_API_KEY": "eq5t4qqajzvgewyq",
        "KITE_API_SECRET": "mqhu4z7mmemxtymd1dvuusiti8v8dpgb",
        "KITE_REQUEST_TOKEN": "tPUoDAxOWc1YX03THvyXuX1kHOoZcmgT"
    }
    }

    async with MCPServerStdio(params=fetch_params) as server:
        fetch_tools = await server.list_tools()
        
        for tool in fetch_tools:
            description = tool.description.replace("\n", " ")
            print(f"{tool.name}: {description}")

# Run the async function
asyncio.run(main())
