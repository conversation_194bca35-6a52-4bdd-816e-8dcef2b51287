{"KiteConnect": {"KiteConnect": "Main class to interact with the Kite Connect API.", "__init__": "Initializes the KiteConnect instance with the API key.", "basket_order_margins": "Fetches margin requirements for basket orders.", "cancel_mf_order": "Cancels a mutual fund order.", "cancel_mf_sip": "Cancels a mutual fund SIP.", "cancel_order": "Cancels an open order.", "convert_position": "Converts a position from intraday to delivery or vice versa.", "delete_gtt": "Deletes a GTT (<PERSON> Till Triggered) order.", "exit_order": "Exits a bracket or cover order.", "generate_session": "Generates a new session using request token and API secret.", "get_gtt": "Retrieves a specific GTT order.", "get_gtts": "Fetches all GTT orders placed by the user.", "historical_data": "Fetches historical candle data for an instrument.", "holdings": "Returns current holdings of the user.", "instruments": "Lists all instruments available for trading.", "invalidate_access_token": "Invalidates the current access token.", "invalidate_refresh_token": "Invalidates the current refresh token.", "login_url": "Returns the login URL for the user to authenticate.", "ltp": "Gets last traded price for instruments.", "margins": "Returns the user's current margins.", "mf_holdings": "Fetches user's mutual fund holdings.", "mf_instruments": "Lists all mutual fund instruments.", "mf_orders": "Fetches mutual fund order history.", "mf_sips": "Lists all mutual fund SIPs.", "modify_gtt": "Modifies an existing GTT order.", "modify_mf_sip": "Modifies a mutual fund SIP.", "modify_order": "Modifies an existing open order.", "ohlc": "Gets OHLC (open-high-low-close) data for instruments.", "order_history": "Retrieves history for a specific order.", "order_margins": "Fetches margin requirements for placing an order.", "order_trades": "Lists trades executed for a specific order.", "orders": "Fetches all orders placed by the user.", "place_gtt": "Places a new GTT order.", "place_mf_order": "Places a mutual fund order.", "place_mf_sip": "Places a mutual fund SIP.", "place_order": "Places a new order.", "positions": "Gets the user's positions (day and net).", "profile": "Fetches user profile details.", "quote": "Fetches market quote for given instruments.", "renew_access_token": "Renews the access token using a refresh token.", "set_access_token": "Sets the access token for session authentication.", "set_session_expiry_hook": "Sets a hook function to run when session expires.", "trades": "Lists all trades executed by the user.", "trigger_range": "Fetches trigger range for SL orders of an instrument."}, "KiteTicker": {"KiteTicker": "WebSocket client to stream live market data.", "__init__": "Initializes the KiteTicker instance.", "close": "Closes the WebSocket connection.", "connect": "Connects to the Kite WebSocket server.", "is_connected": "Checks if the WebSocket is connected.", "resubscribe": "Resubscribes to existing WebSocket tokens.", "set_mode": "Sets streaming mode for the subscribed tokens.", "stop": "Stops the WebSocket client.", "stop_retry": "Stops automatic reconnection attempts.", "subscribe": "Subscribes to new market data tokens.", "unsubscribe": "Unsubscribes from existing market data tokens."}, "Sub-modules": {"kiteconnect.exceptions": "Contains exceptions used by the KiteConnect SDK."}}