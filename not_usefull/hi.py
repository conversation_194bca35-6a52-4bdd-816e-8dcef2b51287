from kiteconnect import KiteConnect
import instructor
from openai import OpenAI
import os
os.environ['OPENAI_API_KEY'] = "********************************************************************************************************************************************************************" 
client = OpenAI()
client = instructor.patch(client)

# Step 1: Kite API setup
kite_api_key = "eq5t4qqajzvgewyq"
kite_api_secret = "mqhu4z7mmemxtymd1dvuusiti8v8dpgb"
kite = KiteConnect(api_key=kite_api_key)

# After user logs in, you get a request_token manually from the URL
request_token = "da22fiL8tVc21YqoP7VpuEqNGPLyDrSQ"
data = kite.generate_session(request_token, api_secret=kite_api_secret)
kite.set_access_token(data["access_token"])

# Example: Get your Zerodha holdings
holdings = kite.holdings()
print("Your Holdings:", holdings)


def ask_openai_about_holdings(holdings):
    response = client.chat.completions.create(
        model="o3-mini",
        messages=[
            {"role": "system", "content": "You're a finance assistant."},
            {"role": "user", "content": f"My current stock holdings are: {holdings}. What do you suggest?"}
        ]
    )
    
    return response.choices[0].message.content.strip()

# Send holdings to OpenAI for analysis
advice = ask_openai_about_holdings(holdings)
print("AI Advice:", advice)
