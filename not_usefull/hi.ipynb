{"cells": [{"cell_type": "code", "execution_count": 12, "id": "6b672692", "metadata": {}, "outputs": [], "source": ["from agents import Agent\n", "\n", "history_tutor_agent = Agent(\n", "    name=\"<PERSON> Tutor\",\n", "    handoff_description=\"Specialist agent for historical questions\",\n", "    instructions=\"You provide assistance with historical queries. Explain important events and context clearly.\",\n", ")\n", "\n", "math_tutor_agent = Agent(\n", "    name=\"<PERSON>\",\n", "    handoff_description=\"Specialist agent for math questions\",\n", "    instructions=\"You provide help with math problems. Explain your reasoning at each step and include examples\",\n", ")\n", "\n", "triage_agent = Agent(\n", "    name=\"Triage Agent\",\n", "    instructions=\"You determine which agent to use based on the user's homework question\",\n", "    handoffs=[history_tutor_agent, math_tutor_agent]\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "359519fb", "metadata": {}, "outputs": [], "source": ["from agents import Runner\n", "\n", "async def main():\n", "    result = await Runner.run(triage_agent, \"What is the capital of France?\")\n", "    print(result.final_output)"]}, {"cell_type": "code", "execution_count": null, "id": "98d7caed", "metadata": {}, "outputs": [], "source": ["from kiteconnect import KiteConnect\n", "import instructor\n", "from openai import OpenAI\n", "import os\n", "os.environ['OPENAI_API_KEY'] = \"********************************************************************************************************************************************************************\" \n", "client = OpenAI()\n", "client = instructor.patch(client)\n", "\n", "# Step 1: Kite API setup\n", "kite_api_key = \"eq5t4qqajzvgewyq\"\n", "kite_api_secret = \"mqhu4z7mmemxtymd1dvuusiti8v8dpgb\"\n", "kite = KiteConnect(api_key=kite_api_key)\n", "\n", "# After user logs in, you get a request_token manually from the URL\n", "request_token = \"\"\n", "data = kite.generate_session(request_token, api_secret=kite_api_secret)\n", "kite.set_access_token(data[\"access_token\"])\n", "\n", "# Example: Get your Zerodha holdings\n", "holdings = kite.holdings()\n", "print(\"Your Holdings:\", holdings)"]}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}