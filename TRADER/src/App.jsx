import { useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import LoginPage from './components/LoginPage'
import ConnectPage from './components/ConnectPage'
import Dashboard from './components/Dashboard'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isKiteConnected, setIsKiteConnected] = useState(false)
  const [holdings, setHoldings] = useState(null)

  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <Routes>
          <Route
            path="/"
            element={
              !isAuthenticated ? (
                <LoginPage onLogin={() => setIsAuthenticated(true)} />
              ) : (
                <Navigate to="/connect" replace />
              )
            }
          />
          <Route
            path="/connect"
            element={
              isAuthenticated ? (
                !isKiteConnected ? (
                  <ConnectPage
                    onConnect={(holdingsData) => {
                      setIsKiteConnected(true)
                      setHoldings(holdingsData)
                    }}
                  />
                ) : (
                  <Navigate to="/dashboard" replace />
                )
              ) : (
                <Navigate to="/" replace />
              )
            }
          />
          <Route
            path="/dashboard"
            element={
              isAuthenticated && isKiteConnected ? (
                <Dashboard holdings={holdings} />
              ) : (
                <Navigate to="/" replace />
              )
            }
          />
        </Routes>
      </div>
    </Router>
  )
}

export default App
