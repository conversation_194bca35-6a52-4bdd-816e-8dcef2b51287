import { useState, useEffect } from 'react'
import HoldingsDisplay from './HoldingsDisplay'
import ChatInterface from './ChatInterface'
import OrdersDisplay from './OrdersDisplay'
import TradesDisplay from './TradesDisplay'
import MarketWatch from './MarketWatch'
import PortfolioAnalytics from './PortfolioAnalytics'
import TradingInterface from './TradingInterface'
import axios from 'axios'
import { API_URL } from '../config'

const Dashboard = ({ holdings }) => {
  const [activeTab, setActiveTab] = useState('overview')
  const [orders, setOrders] = useState([])
  const [trades, setTrades] = useState([])
  const [profile, setProfile] = useState(null)
  const [margins, setMargins] = useState(null)
  const [loading, setLoading] = useState(false)

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'holdings', name: 'Holdings', icon: '💼' },
    { id: 'orders', name: 'Orders', icon: '📋' },
    { id: 'trades', name: 'Trades', icon: '💱' },
    { id: 'trading', name: 'Trade', icon: '🚀' },
    { id: 'analytics', name: 'Analytics', icon: '📈' },
    { id: 'market', name: 'Market Watch', icon: '👁️' },
    { id: 'chat', name: 'AI Assistant', icon: '🤖' }
  ]

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    setLoading(true)
    try {
      const [ordersRes, tradesRes, profileRes, marginsRes] = await Promise.allSettled([
        axios.get(`${API_URL}/orders`),
        axios.get(`${API_URL}/trades`),
        axios.get(`${API_URL}/profile`),
        axios.get(`${API_URL}/margins`)
      ])

      if (ordersRes.status === 'fulfilled') setOrders(ordersRes.value.data.orders || [])
      if (tradesRes.status === 'fulfilled') setTrades(tradesRes.value.data.trades || [])
      if (profileRes.status === 'fulfilled') setProfile(profileRes.value.data.profile || null)
      if (marginsRes.status === 'fulfilled') setMargins(marginsRes.value.data.margins || null)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const refreshData = () => {
    fetchDashboardData()
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">Trading Dashboard</h1>
              <p className="text-gray-600">
                {profile ? `Welcome back, ${profile.user_name || profile.email}` : 'Manage your portfolio and get AI-powered insights'}
              </p>
            </div>
            <button
              onClick={refreshData}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2"
            >
              <span className={loading ? 'animate-spin' : ''}>🔄</span>
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-xl shadow-lg mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-1 px-6 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-4 border-b-2 font-medium text-sm whitespace-nowrap flex items-center space-x-2 ${activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <PortfolioAnalytics
                holdings={holdings}
                orders={orders}
                trades={trades}
                profile={profile}
                margins={margins}
              />
            )}
            {activeTab === 'holdings' && <HoldingsDisplay holdings={holdings} />}
            {activeTab === 'orders' && <OrdersDisplay orders={orders} onRefresh={refreshData} />}
            {activeTab === 'trades' && <TradesDisplay trades={trades} />}
            {activeTab === 'trading' && <TradingInterface onOrderPlaced={refreshData} />}
            {activeTab === 'analytics' && <PortfolioAnalytics holdings={holdings} detailed={true} />}
            {activeTab === 'market' && <MarketWatch />}
            {activeTab === 'chat' && <ChatInterface />}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
