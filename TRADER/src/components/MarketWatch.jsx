import { useState, useEffect } from 'react'
import axios from 'axios'
import { API_URL } from '../config'

const MarketWatch = () => {
  const [watchlist, setWatchlist] = useState([
    'NSE:RELIANCE', 'NSE:TCS', 'NSE:HDFCBANK', 'NSE:INFY', 'NSE:ICICIBANK',
    'NSE:SBIN', 'NSE:BHARTIARTL', 'NSE:ITC', 'NSE:KOTAKBANK', 'NSE:LT'
  ])
  const [quotes, setQuotes] = useState({})
  const [loading, setLoading] = useState(false)
  const [newSymbol, setNewSymbol] = useState('')
  const [selectedExchange, setSelectedExchange] = useState('NSE')

  const exchanges = ['NSE', 'BSE']

  useEffect(() => {
    if (watchlist.length > 0) {
      fetchQuotes()
    }
  }, [watchlist])

  const fetchQuotes = async () => {
    if (watchlist.length === 0) return
    
    setLoading(true)
    try {
      const response = await axios.post(`${API_URL}/quote`, {
        instruments: watchlist
      })
      setQuotes(response.data.quotes || {})
    } catch (error) {
      console.error('Error fetching quotes:', error)
    } finally {
      setLoading(false)
    }
  }

  const addToWatchlist = () => {
    if (!newSymbol.trim()) return
    
    const instrument = `${selectedExchange}:${newSymbol.toUpperCase()}`
    if (!watchlist.includes(instrument)) {
      setWatchlist(prev => [...prev, instrument])
    }
    setNewSymbol('')
  }

  const removeFromWatchlist = (instrument) => {
    setWatchlist(prev => prev.filter(item => item !== instrument))
    setQuotes(prev => {
      const newQuotes = { ...prev }
      delete newQuotes[instrument]
      return newQuotes
    })
  }

  const getChangeColor = (change) => {
    if (change > 0) return 'text-green-600'
    if (change < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getChangeIcon = (change) => {
    if (change > 0) return '▲'
    if (change < 0) return '▼'
    return '●'
  }

  const formatNumber = (num) => {
    if (num >= 10000000) return (num / 10000000).toFixed(2) + 'Cr'
    if (num >= 100000) return (num / 100000).toFixed(2) + 'L'
    if (num >= 1000) return (num / 1000).toFixed(2) + 'K'
    return num?.toFixed(2) || '0.00'
  }

  return (
    <div className="space-y-6">
      {/* Add to Watchlist */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Add to Watchlist</h3>
        <div className="flex space-x-3">
          <select
            value={selectedExchange}
            onChange={(e) => setSelectedExchange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
          >
            {exchanges.map(exchange => (
              <option key={exchange} value={exchange}>{exchange}</option>
            ))}
          </select>
          <input
            type="text"
            value={newSymbol}
            onChange={(e) => setNewSymbol(e.target.value)}
            placeholder="Enter symbol (e.g., RELIANCE)"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
            onKeyPress={(e) => e.key === 'Enter' && addToWatchlist()}
          />
          <button
            onClick={addToWatchlist}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Add
          </button>
          <button
            onClick={fetchQuotes}
            disabled={loading}
            className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
          >
            <span className={loading ? 'animate-spin' : ''}>🔄</span>
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Market Watch Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800">Market Watch</h3>
        </div>
        
        {loading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading quotes...</span>
          </div>
        )}

        {!loading && (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LTP</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">% Change</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Volume</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">High/Low</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {watchlist.map((instrument) => {
                  const quote = quotes[instrument]
                  const symbol = instrument.split(':')[1]
                  const exchange = instrument.split(':')[0]
                  
                  if (!quote) {
                    return (
                      <tr key={instrument} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{symbol}</div>
                            <div className="text-sm text-gray-500">{exchange}</div>
                          </div>
                        </td>
                        <td colSpan="6" className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          Loading...
                        </td>
                      </tr>
                    )
                  }

                  const change = quote.net_change || 0
                  const changePercent = quote.ohlc?.open ? ((quote.last_price - quote.ohlc.open) / quote.ohlc.open * 100) : 0

                  return (
                    <tr key={instrument} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{symbol}</div>
                          <div className="text-sm text-gray-500">{exchange}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ₹{quote.last_price?.toFixed(2) || '0.00'}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${getChangeColor(change)}`}>
                        <div className="flex items-center space-x-1">
                          <span>{getChangeIcon(change)}</span>
                          <span>{Math.abs(change).toFixed(2)}</span>
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${getChangeColor(changePercent)}`}>
                        {changePercent >= 0 ? '+' : ''}{changePercent.toFixed(2)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatNumber(quote.volume)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="text-green-600">H: ₹{quote.ohlc?.high?.toFixed(2) || '0.00'}</div>
                          <div className="text-red-600">L: ₹{quote.ohlc?.low?.toFixed(2) || '0.00'}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button
                          onClick={() => removeFromWatchlist(instrument)}
                          className="text-red-600 hover:text-red-800 transition-colors"
                        >
                          Remove
                        </button>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        )}

        {!loading && watchlist.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">👁️</div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No Stocks in Watchlist</h3>
            <p className="text-gray-500">Add stocks to your watchlist to monitor their prices.</p>
          </div>
        )}
      </div>

      {/* Market Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Market Status</h4>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-900">Market Open</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">9:15 AM - 3:30 PM</p>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Gainers</h4>
          <p className="text-lg font-semibold text-green-600">
            {Object.values(quotes).filter(q => (q.net_change || 0) > 0).length}
          </p>
          <p className="text-xs text-gray-500">stocks up</p>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Losers</h4>
          <p className="text-lg font-semibold text-red-600">
            {Object.values(quotes).filter(q => (q.net_change || 0) < 0).length}
          </p>
          <p className="text-xs text-gray-500">stocks down</p>
        </div>
      </div>
    </div>
  )
}

export default MarketWatch
