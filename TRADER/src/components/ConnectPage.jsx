import { useState, useEffect } from 'react'
import axios from 'axios'
import { API_URL } from '../config'

const ConnectPage = ({ onConnect }) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [kiteLoginUrl, setKiteLoginUrl] = useState('')
  const [requestToken, setRequestToken] = useState('')
  const [showTokenInput, setShowTokenInput] = useState(false)

  const getKiteLoginUrl = async () => {
    try {
      setKiteLoginUrl("https://kite.zerodha.com/connect/login?api_key=eq5t4qqajzvgewyq&v=3")
    } catch (err) {
      setError('Failed to get Kite login URL')
    }
  }

  const handleKiteConnect = () => {
    if (kiteLoginUrl) {
      window.open(kiteLoginUrl, '_blank')
      setShowTokenInput(true)
    }
  }

  const handleTokenSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await axios.post(`${API_URL}/kite-connect`, {
        request_token: requestToken
      })

      if (response.data.success) {
        onConnect(response.data.holdings)
      }
    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to connect to Kite')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getKiteLoginUrl()
  }, [])

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl p-8 w-full max-w-lg">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Connect to Kite</h1>
          <p className="text-gray-600">Connect your Zerodha account to access your portfolio</p>
        </div>

        {!showTokenInput ? (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-2">How to connect:</h3>
              <ol className="text-sm text-blue-700 space-y-1">
                <li>1. Click "Connect to Kite" button below</li>
                <li>2. Login to your Zerodha account</li>
                <li>3. Copy the request token from the URL</li>
                <li>4. Paste it here to complete the connection</li>
              </ol>
            </div>

            <button
              onClick={handleKiteConnect}
              disabled={!kiteLoginUrl}
              className="w-full bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
            >
              {kiteLoginUrl ? 'Connect to Kite' : 'Loading...'}
              {console.log(kiteLoginUrl)}
            </button>
          </div>
        ) : (
          <form onSubmit={handleTokenSubmit} className="space-y-6">
            <div>
              <label htmlFor="token" className="block text-sm font-medium text-gray-700 mb-2">
                Request Token
              </label>
              <input
                type="text"
                id="token"
                value={requestToken}
                onChange={(e) => setRequestToken(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent outline-none transition-all"
                placeholder="Paste the request token from Kite URL"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Look for "request_token=" in the URL after logging in to Kite
              </p>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
            >
              {loading ? 'Connecting...' : 'Complete Connection'}
            </button>
          </form>
        )}

        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            Your credentials are secure and used only for portfolio access
          </p>
        </div>
      </div>
    </div>
  )
}

export default ConnectPage
