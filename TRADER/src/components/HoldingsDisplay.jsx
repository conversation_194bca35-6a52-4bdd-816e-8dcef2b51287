const HoldingsDisplay = ({ holdings }) => {
  // Safely normalize holdings into an array
  const holdingsArray = Array.isArray(holdings)
    ? holdings
    : typeof holdings === 'object' && holdings !== null
    ? Object.values(holdings)
    : [];

  if (!holdingsArray.length) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">📊</div>
        <h3 className="text-xl font-semibold text-gray-600 mb-2">No Holdings Found</h3>
        <p className="text-gray-500">Your portfolio appears to be empty or data couldn't be loaded.</p>
      </div>
    );
  }

  const calculateTotalValue = () => {
    return holdingsArray.reduce((total, h) => {
      return total + ((h.last_price ?? 0) * (h.quantity ?? 0));
    }, 0);
  };

  const calculateTotalPnL = () => {
    return holdingsArray.reduce((total, h) => {
      const current = (h.last_price ?? 0) * (h.quantity ?? 0);
      const invested = (h.average_price ?? 0) * (h.quantity ?? 0);
      return total + (current - invested);
    }, 0);
  };

  const totalValue = calculateTotalValue();
  const totalPnL = calculateTotalPnL();
  const pnlPercentage = totalValue > 0 ? (totalPnL / (totalValue - totalPnL)) * 100 : 0;
  console.log(holdingsArray)
  return (
    <div className="space-y-6">
      {/* Portfolio Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-1">Total Portfolio Value</h3>
          <p className="text-2xl font-bold text-blue-900">
            ₹{totalValue.toLocaleString('en-IN', { maximumFractionDigits: 2 })}
          </p>
        </div>
        <div className={`border rounded-lg p-4 ${totalPnL >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <h3 className={`text-sm font-medium mb-1 ${totalPnL >= 0 ? 'text-green-800' : 'text-red-800'}`}>Total P&L</h3>
          <p className={`text-2xl font-bold ${totalPnL >= 0 ? 'text-green-900' : 'text-red-900'}`}>
            {totalPnL >= 0 ? '+' : ''}₹{totalPnL.toLocaleString('en-IN', { maximumFractionDigits: 2 })}
          </p>
        </div>
        <div className={`border rounded-lg p-4 ${pnlPercentage >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <h3 className={`text-sm font-medium mb-1 ${pnlPercentage >= 0 ? 'text-green-800' : 'text-red-800'}`}>P&L %</h3>
          <p className={`text-2xl font-bold ${pnlPercentage >= 0 ? 'text-green-900' : 'text-red-900'}`}>
            {pnlPercentage >= 0 ? '+' : ''}{pnlPercentage.toFixed(2)}%
          </p>
        </div>
      </div>

      {/* Holdings Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Price</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LTP</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Value</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {holdingsArray.map((h, index) => {
                const currentValue = (h.last_price ?? 0) * (h.quantity ?? 0);
                const investedValue = (h.average_price ?? 0) * (h.quantity ?? 0);
                const pnl = currentValue - investedValue;
                const pnlPercent = investedValue > 0 ? (pnl / investedValue) * 100 : 0;

                return (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{h.tradingsymbol ?? '-'}</div>
                        <div className="text-sm text-gray-500">{h.exchange ?? '-'}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{h.quantity ?? 0}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ₹{(h.average_price ?? 0).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ₹{(h.last_price ?? 0).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ₹{currentValue.toLocaleString('en-IN', { maximumFractionDigits: 2 })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className={pnl >= 0 ? 'text-green-600' : 'text-red-600'}>
                        <div className="font-medium">
                          {pnl >= 0 ? '+' : ''}₹{pnl.toFixed(2)}
                        </div>
                        <div className="text-xs">
                          ({pnl >= 0 ? '+' : ''}{pnlPercent.toFixed(2)}%)
                        </div>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default HoldingsDisplay;
