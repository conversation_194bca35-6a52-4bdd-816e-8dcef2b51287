import { useMemo } from 'react'

const PortfolioAnalytics = ({ holdings, orders, trades, profile, margins, detailed = false }) => {
  const portfolioMetrics = useMemo(() => {
    if (!holdings) return null

    const holdingsData = Array.isArray(holdings) ? holdings : 
                        (holdings.net ? holdings.net : Object.values(holdings))

    let totalInvested = 0
    let totalCurrent = 0
    let totalPnL = 0
    let winners = 0
    let losers = 0
    let sectorAllocation = {}
    let topHoldings = []

    holdingsData.forEach(holding => {
      if (typeof holding === 'object' && holding !== null) {
        const quantity = holding.quantity || 0
        const avgPrice = holding.average_price || 0
        const ltp = holding.last_price || 0
        
        const invested = quantity * avgPrice
        const current = quantity * ltp
        const pnl = current - invested

        totalInvested += invested
        totalCurrent += current
        totalPnL += pnl

        if (pnl > 0) winners++
        else if (pnl < 0) losers++

        // Simple sector classification
        const symbol = holding.tradingsymbol || ''
        let sector = 'Others'
        if (['HDFCBANK', 'ICICIBANK', 'SBIN', 'AXISBANK', 'KOTAKBANK'].some(bank => symbol.includes(bank))) {
          sector = 'Banking'
        } else if (['TCS', 'INFY', 'WIPRO', 'TECHM', 'HCLTECH'].some(it => symbol.includes(it))) {
          sector = 'IT'
        } else if (['RELIANCE', 'ONGC', 'IOC', 'BPCL'].some(energy => symbol.includes(energy))) {
          sector = 'Energy'
        } else if (['ITC', 'HINDUNILVR', 'NESTLEIND', 'BRITANNIA'].some(fmcg => symbol.includes(fmcg))) {
          sector = 'FMCG'
        }

        sectorAllocation[sector] = (sectorAllocation[sector] || 0) + current

        topHoldings.push({
          symbol: symbol,
          current: current,
          pnl: pnl,
          pnlPercent: invested > 0 ? (pnl / invested) * 100 : 0
        })
      }
    })

    // Sort top holdings by current value
    topHoldings.sort((a, b) => b.current - a.current)

    // Calculate sector percentages
    const sectorPercentages = {}
    Object.keys(sectorAllocation).forEach(sector => {
      sectorPercentages[sector] = totalCurrent > 0 ? (sectorAllocation[sector] / totalCurrent) * 100 : 0
    })

    return {
      totalInvested,
      totalCurrent,
      totalPnL,
      totalPnLPercent: totalInvested > 0 ? (totalPnL / totalInvested) * 100 : 0,
      winners,
      losers,
      totalStocks: holdingsData.length,
      sectorAllocation: sectorPercentages,
      topHoldings: topHoldings.slice(0, 5)
    }
  }, [holdings])

  const marginMetrics = useMemo(() => {
    if (!margins) return null

    const equity = margins.equity || {}
    return {
      available: equity.available?.cash || 0,
      utilised: equity.utilised?.debits || 0,
      total: (equity.available?.cash || 0) + (equity.utilised?.debits || 0)
    }
  }, [margins])

  if (!portfolioMetrics) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">📊</div>
        <h3 className="text-xl font-semibold text-gray-600 mb-2">No Portfolio Data</h3>
        <p className="text-gray-500">Portfolio analytics will appear here once you have holdings.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-1">Portfolio Value</h3>
          <p className="text-2xl font-bold text-blue-900">
            ₹{portfolioMetrics.totalCurrent.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
          </p>
          <p className="text-xs text-blue-600">
            Invested: ₹{portfolioMetrics.totalInvested.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
          </p>
        </div>
        
        <div className={`border rounded-lg p-4 ${
          portfolioMetrics.totalPnL >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
        }`}>
          <h3 className={`text-sm font-medium mb-1 ${
            portfolioMetrics.totalPnL >= 0 ? 'text-green-800' : 'text-red-800'
          }`}>
            Total P&L
          </h3>
          <p className={`text-2xl font-bold ${
            portfolioMetrics.totalPnL >= 0 ? 'text-green-900' : 'text-red-900'
          }`}>
            {portfolioMetrics.totalPnL >= 0 ? '+' : ''}₹{portfolioMetrics.totalPnL.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
          </p>
          <p className={`text-xs ${
            portfolioMetrics.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {portfolioMetrics.totalPnLPercent >= 0 ? '+' : ''}{portfolioMetrics.totalPnLPercent.toFixed(2)}%
          </p>
        </div>

        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-purple-800 mb-1">Holdings</h3>
          <p className="text-2xl font-bold text-purple-900">{portfolioMetrics.totalStocks}</p>
          <p className="text-xs text-purple-600">
            {portfolioMetrics.winners}W / {portfolioMetrics.losers}L
          </p>
        </div>

        {marginMetrics && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-orange-800 mb-1">Available Margin</h3>
            <p className="text-2xl font-bold text-orange-900">
              ₹{marginMetrics.available.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
            </p>
            <p className="text-xs text-orange-600">
              Used: ₹{marginMetrics.utilised.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
            </p>
          </div>
        )}
      </div>

      {detailed && (
        <>
          {/* Sector Allocation */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Sector Allocation</h3>
            <div className="space-y-3">
              {Object.entries(portfolioMetrics.sectorAllocation).map(([sector, percentage]) => (
                <div key={sector} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{sector}</span>
                  <div className="flex items-center space-x-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">
                      {percentage.toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Holdings */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Top Holdings</h3>
            <div className="space-y-3">
              {portfolioMetrics.topHoldings.map((holding, index) => (
                <div key={holding.symbol} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                    <span className="text-sm font-semibold text-gray-800">{holding.symbol}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      ₹{holding.current.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
                    </div>
                    <div className={`text-xs ${holding.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {holding.pnl >= 0 ? '+' : ''}₹{holding.pnl.toLocaleString('en-IN', { maximumFractionDigits: 0 })} 
                      ({holding.pnlPercent >= 0 ? '+' : ''}{holding.pnlPercent.toFixed(1)}%)
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Quick Stats */}
      {!detailed && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Orders Today</span>
                <span className="text-sm font-medium text-gray-900">
                  {orders ? orders.filter(o => {
                    const today = new Date().toDateString()
                    return new Date(o.order_timestamp || 0).toDateString() === today
                  }).length : 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Trades Today</span>
                <span className="text-sm font-medium text-gray-900">
                  {trades ? trades.filter(t => {
                    const today = new Date().toDateString()
                    return new Date(t.fill_timestamp || 0).toDateString() === today
                  }).length : 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Win Rate</span>
                <span className="text-sm font-medium text-gray-900">
                  {portfolioMetrics.totalStocks > 0 
                    ? ((portfolioMetrics.winners / portfolioMetrics.totalStocks) * 100).toFixed(1)
                    : 0}%
                </span>
              </div>
            </div>
          </div>

          {/* Performance Summary */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Performance Summary</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Best Performer</span>
                <span className="text-sm font-medium text-green-600">
                  {portfolioMetrics.topHoldings[0]?.symbol || 'N/A'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Largest Holding</span>
                <span className="text-sm font-medium text-gray-900">
                  {portfolioMetrics.topHoldings[0]?.symbol || 'N/A'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Diversification</span>
                <span className="text-sm font-medium text-gray-900">
                  {Object.keys(portfolioMetrics.sectorAllocation).length} sectors
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PortfolioAnalytics
