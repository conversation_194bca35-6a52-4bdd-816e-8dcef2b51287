import { useState, useMemo } from 'react'

const TradesDisplay = ({ trades }) => {
  const [sortBy, setSortBy] = useState('trade_id')
  const [sortOrder, setSortOrder] = useState('desc')

  const getTransactionColor = (type) => {
    return type?.toLowerCase() === 'buy' ? 'text-green-600' : 'text-red-600'
  }

  const tradeStats = useMemo(() => {
    if (!trades.length) return { totalTrades: 0, totalValue: 0, buyTrades: 0, sellTrades: 0 }

    const stats = trades.reduce((acc, trade) => {
      const value = (trade.price || 0) * (trade.quantity || 0)
      acc.totalValue += value
      acc.totalTrades += 1
      
      if (trade.transaction_type?.toLowerCase() === 'buy') {
        acc.buyTrades += 1
      } else {
        acc.sellTrades += 1
      }
      
      return acc
    }, { totalTrades: 0, totalValue: 0, buyTrades: 0, sellTrades: 0 })

    return stats
  }, [trades])

  const sortedTrades = useMemo(() => {
    return [...trades].sort((a, b) => {
      let aValue = a[sortBy]
      let bValue = b[sortBy]

      // Handle numeric sorting
      if (sortBy === 'price' || sortBy === 'quantity' || sortBy === 'trade_id') {
        aValue = parseFloat(aValue) || 0
        bValue = parseFloat(bValue) || 0
      }

      // Handle date sorting
      if (sortBy === 'fill_timestamp') {
        aValue = new Date(aValue || 0)
        bValue = new Date(bValue || 0)
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }, [trades, sortBy, sortOrder])

  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('desc')
    }
  }

  const SortIcon = ({ column }) => {
    if (sortBy !== column) return <span className="text-gray-400">↕️</span>
    return <span>{sortOrder === 'asc' ? '↑' : '↓'}</span>
  }

  if (!trades.length) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">💱</div>
        <h3 className="text-xl font-semibold text-gray-600 mb-2">No Trades Found</h3>
        <p className="text-gray-500">No trades have been executed yet.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Trade Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-1">Total Trades</h3>
          <p className="text-2xl font-bold text-blue-900">{tradeStats.totalTrades}</p>
        </div>
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-purple-800 mb-1">Total Value</h3>
          <p className="text-2xl font-bold text-purple-900">
            ₹{tradeStats.totalValue.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
          </p>
        </div>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-green-800 mb-1">Buy Trades</h3>
          <p className="text-2xl font-bold text-green-900">{tradeStats.buyTrades}</p>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-red-800 mb-1">Sell Trades</h3>
          <p className="text-2xl font-bold text-red-900">{tradeStats.sellTrades}</p>
        </div>
      </div>

      {/* Trades Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('trade_id')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Trade ID</span>
                    <SortIcon column="trade_id" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('quantity')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Quantity</span>
                    <SortIcon column="quantity" />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('price')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Price</span>
                    <SortIcon column="price" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('fill_timestamp')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Time</span>
                    <SortIcon column="fill_timestamp" />
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedTrades.map((trade, index) => {
                const tradeValue = (trade.price || 0) * (trade.quantity || 0)
                return (
                  <tr key={trade.trade_id || index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {trade.trade_id || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{trade.tradingsymbol || '-'}</div>
                        <div className="text-sm text-gray-500">{trade.exchange || '-'}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${getTransactionColor(trade.transaction_type)}`}>
                        {trade.transaction_type || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {trade.quantity || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ₹{(trade.price || 0).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ₹{tradeValue.toLocaleString('en-IN', { maximumFractionDigits: 2 })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {trade.fill_timestamp ? new Date(trade.fill_timestamp).toLocaleString() : '-'}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default TradesDisplay
