import { useState } from 'react'
import axios from 'axios'
import { API_URL } from '../config'

const TradingInterface = ({ onOrderPlaced }) => {
  const [orderForm, setOrderForm] = useState({
    tradingsymbol: '',
    exchange: 'NSE',
    transaction_type: 'BUY',
    order_type: 'MARKET',
    quantity: '',
    price: '',
    trigger_price: '',
    product: 'CNC'
  })
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState('')

  const exchanges = ['NSE', 'BSE', 'NFO', 'CDS', 'MCX']
  const orderTypes = ['MARKET', 'LIMIT', 'SL', 'SL-M']
  const products = ['CNC', 'MIS', 'NRML']

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setOrderForm(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const showMessage = (msg, type) => {
    setMessage(msg)
    setMessageType(type)
    setTimeout(() => {
      setMessage('')
      setMessageType('')
    }, 5000)
  }

  const validateForm = () => {
    if (!orderForm.tradingsymbol.trim()) {
      showMessage('Please enter a trading symbol', 'error')
      return false
    }
    if (!orderForm.quantity || orderForm.quantity <= 0) {
      showMessage('Please enter a valid quantity', 'error')
      return false
    }
    if ((orderForm.order_type === 'LIMIT' || orderForm.order_type === 'SL') && (!orderForm.price || orderForm.price <= 0)) {
      showMessage('Please enter a valid price for limit orders', 'error')
      return false
    }
    if ((orderForm.order_type === 'SL' || orderForm.order_type === 'SL-M') && (!orderForm.trigger_price || orderForm.trigger_price <= 0)) {
      showMessage('Please enter a valid trigger price for stop loss orders', 'error')
      return false
    }
    return true
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {
      const orderData = {
        ...orderForm,
        quantity: parseInt(orderForm.quantity),
        price: orderForm.price ? parseFloat(orderForm.price) : null,
        trigger_price: orderForm.trigger_price ? parseFloat(orderForm.trigger_price) : null
      }

      const response = await axios.post(`${API_URL}/place-order`, orderData)
      
      if (response.data.success) {
        showMessage(`Order placed successfully! Order ID: ${response.data.order_id}`, 'success')
        setOrderForm({
          tradingsymbol: '',
          exchange: 'NSE',
          transaction_type: 'BUY',
          order_type: 'MARKET',
          quantity: '',
          price: '',
          trigger_price: '',
          product: 'CNC'
        })
        if (onOrderPlaced) onOrderPlaced()
      }
    } catch (error) {
      const errorMsg = error.response?.data?.detail || 'Failed to place order'
      showMessage(errorMsg, 'error')
    } finally {
      setLoading(false)
    }
  }

  const getQuote = async () => {
    if (!orderForm.tradingsymbol.trim()) {
      showMessage('Please enter a trading symbol first', 'error')
      return
    }

    try {
      const response = await axios.post(`${API_URL}/quote`, {
        instruments: [`${orderForm.exchange}:${orderForm.tradingsymbol}`]
      })
      
      const quote = response.data.quotes[`${orderForm.exchange}:${orderForm.tradingsymbol}`]
      if (quote) {
        showMessage(`LTP: ₹${quote.last_price} | Change: ${quote.net_change} (${quote.ohlc?.open ? ((quote.last_price - quote.ohlc.open) / quote.ohlc.open * 100).toFixed(2) : 0}%)`, 'info')
      }
    } catch (error) {
      showMessage('Failed to get quote', 'error')
    }
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">Place Order</h2>
        
        {message && (
          <div className={`mb-4 p-4 rounded-lg ${
            messageType === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
            messageType === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
            'bg-blue-100 text-blue-800 border border-blue-200'
          }`}>
            {message}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Symbol and Exchange */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trading Symbol
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  name="tradingsymbol"
                  value={orderForm.tradingsymbol}
                  onChange={handleInputChange}
                  placeholder="e.g., RELIANCE, TCS"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  required
                />
                <button
                  type="button"
                  onClick={getQuote}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Quote
                </button>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Exchange
              </label>
              <select
                name="exchange"
                value={orderForm.exchange}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              >
                {exchanges.map(exchange => (
                  <option key={exchange} value={exchange}>{exchange}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Transaction Type and Order Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transaction Type
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="transaction_type"
                    value="BUY"
                    checked={orderForm.transaction_type === 'BUY'}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <span className="text-green-600 font-medium">BUY</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="transaction_type"
                    value="SELL"
                    checked={orderForm.transaction_type === 'SELL'}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <span className="text-red-600 font-medium">SELL</span>
                </label>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Order Type
              </label>
              <select
                name="order_type"
                value={orderForm.order_type}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              >
                {orderTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Quantity and Product */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity
              </label>
              <input
                type="number"
                name="quantity"
                value={orderForm.quantity}
                onChange={handleInputChange}
                placeholder="Enter quantity"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product
              </label>
              <select
                name="product"
                value={orderForm.product}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              >
                {products.map(product => (
                  <option key={product} value={product}>{product}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Price Fields */}
          {(orderForm.order_type === 'LIMIT' || orderForm.order_type === 'SL') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price
              </label>
              <input
                type="number"
                name="price"
                value={orderForm.price}
                onChange={handleInputChange}
                placeholder="Enter price"
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              />
            </div>
          )}

          {(orderForm.order_type === 'SL' || orderForm.order_type === 'SL-M') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trigger Price
              </label>
              <input
                type="number"
                name="trigger_price"
                value={orderForm.trigger_price}
                onChange={handleInputChange}
                placeholder="Enter trigger price"
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              />
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
              orderForm.transaction_type === 'BUY'
                ? 'bg-green-600 hover:bg-green-700 disabled:bg-green-400'
                : 'bg-red-600 hover:bg-red-700 disabled:bg-red-400'
            } text-white`}
          >
            {loading ? 'Placing Order...' : `${orderForm.transaction_type} ${orderForm.tradingsymbol || 'Stock'}`}
          </button>
        </form>
      </div>
    </div>
  )
}

export default TradingInterface
