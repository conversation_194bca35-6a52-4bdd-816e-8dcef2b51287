import { useState, useRef, useEffect } from 'react'
import axios from 'axios'
import { API_URL } from '../config'

const ChatInterface = () => {
  const [messages, setMessages] = useState([
    {
      type: 'bot',
      content: 'Hello! I\'m your AI trading assistant. I can help you analyze your portfolio, answer questions about your holdings, and provide investment insights. What would you like to know?'
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async (e) => {
    e.preventDefault()
    if (!inputMessage.trim() || loading) return

    const userMessage = inputMessage.trim()
    setInputMessage('')

    // Add user message to chat
    setMessages(prev => [...prev, { type: 'user', content: userMessage }])
    setLoading(true)

    try {
      const response = await axios.post(`${API_URL}/chat`, {
        message: userMessage
      })

      // Add bot response to chat
      setMessages(prev => [...prev, { type: 'bot', content: response.data.response }])
    } catch (error) {
      setMessages(prev => [...prev, {
        type: 'bot',
        content: 'Sorry, I encountered an error while processing your request. Please make sure you\'re connected to Kite and try again.'
      }])
    } finally {
      setLoading(false)
    }
  }

  const suggestedQuestions = [
    "What's my portfolio performance?",
    "Which stocks are performing best?",
    "Should I diversify more?",
    "What's my risk exposure?",
    "Any recommendations for my portfolio?",
    "Get me a quote for RELIANCE",
    "Analyze my portfolio risk",
    "What's the current market sentiment?",
    "Should I buy or sell TCS?",
    "How is my sector allocation?"
  ]

  const handleSuggestedQuestion = (question) => {
    setInputMessage(question)
  }

  return (
    <div className="flex flex-col h-[600px]">
      {/* Chat Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-t-lg">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <span className="text-lg">🤖</span>
          </div>
          <div>
            <h3 className="font-semibold">AI Trading Assistant</h3>
            <p className="text-sm opacity-90">Powered by advanced portfolio analysis</p>
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto space-y-4 p-4 bg-gray-50">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg shadow-sm ${message.type === 'user'
                ? 'bg-blue-600 text-white rounded-br-none'
                : 'bg-white text-gray-800 border border-gray-200 rounded-bl-none'
                }`}
            >
              {message.type === 'bot' && (
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xs">🤖</span>
                  <span className="text-xs font-medium text-gray-500">AI Assistant</span>
                </div>
              )}
              <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
              <div className="text-xs opacity-70 mt-2">
                {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          </div>
        ))}

        {loading && (
          <div className="flex justify-start">
            <div className="bg-white text-gray-800 border border-gray-200 px-4 py-3 rounded-lg rounded-bl-none shadow-sm">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-xs">🤖</span>
                <span className="text-xs font-medium text-gray-500">AI Assistant</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">Analyzing your portfolio...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Suggested Questions */}
      {messages.length === 1 && (
        <div className="p-4 bg-white border-t border-gray-200">
          <p className="text-sm font-medium text-gray-700 mb-3">💡 Try asking:</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {suggestedQuestions.map((question, index) => (
              <button
                key={index}
                onClick={() => handleSuggestedQuestion(question)}
                className="text-xs bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 px-3 py-2 rounded-lg hover:from-blue-100 hover:to-purple-100 transition-all duration-200 text-left border border-blue-200 hover:border-blue-300"
              >
                {question}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="p-4 bg-white border-t border-gray-200 rounded-b-lg">
        <form onSubmit={handleSendMessage} className="flex space-x-3">
          <div className="flex-1 relative">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Ask me about your portfolio, market analysis, or trading strategies..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none pr-12"
              disabled={loading}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              💬
            </div>
          </div>
          <button
            type="submit"
            disabled={loading || !inputMessage.trim()}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-400 text-white px-6 py-3 rounded-lg transition-all duration-200 font-medium shadow-sm"
          >
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Sending</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <span>Send</span>
                <span>🚀</span>
              </div>
            )}
          </button>
        </form>
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <span>✨ Enhanced with real-time portfolio analysis</span>
          <span>{messages.length - 1} messages</span>
        </div>
      </div>
    </div>
  )
}

export default ChatInterface
