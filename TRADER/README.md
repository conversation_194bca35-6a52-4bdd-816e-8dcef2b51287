# 🚀 Advanced Trading Dashboard with AI Assistant

A comprehensive, full-featured trading dashboard built with React and FastAPI that integrates with Zerodha Kite Connect API and features an advanced AI-powered assistant for portfolio analysis, trading insights, and market intelligence.

## ✨ Features

### 🔧 Backend (FastAPI)
- **Complete Kite API Integration** - All major Kite Connect functions
  - `/login` - Password authentication
  - `/kite-login-url`, `/kite-connect` - OAuth flow handling
  - `/holdings` - Portfolio holdings data
  - `/orders` - Order management and history
  - `/trades` - Trade execution history
  - `/profile` - User profile information
  - `/margins` - Account margin details
  - `/place-order` - Place new orders
  - `/quote` - Real-time market quotes
  - `/historical-data` - Historical price data
  - `/instruments/{exchange}` - Instrument lists

- **Enhanced AI Chat** - Advanced AI assistant with tools
  - Portfolio analysis and insights
  - Real-time market data access
  - Risk assessment capabilities
  - Trading recommendations
  - Tool-enabled AI for dynamic data fetching

### 🎨 Frontend (React)
- **Multi-Tab Dashboard** - Comprehensive trading interface
  - **Overview** - Portfolio analytics and summary
  - **Holdings** - Detailed portfolio view with P&L
  - **Orders** - Order management and tracking
  - **Trades** - Trade history and analysis
  - **Trading** - Place orders with advanced form
  - **Analytics** - Deep portfolio analysis
  - **Market Watch** - Real-time market monitoring
  - **AI Assistant** - Enhanced chat interface

- **Advanced Components**
  - Real-time data updates
  - Interactive charts and analytics
  - Responsive design with Tailwind CSS
  - Professional UI/UX design

## 🛠️ Setup Instructions

### Prerequisites
- Python 3.8+
- Node.js 16+
- Zerodha Kite API credentials
- OpenAI API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd TRADE
   ```

2. **Backend Setup**
   ```bash
   cd TRADER/python_backend
   pip install fastapi uvicorn kiteconnect openai instructor
   ```

3. **Frontend Setup**
   ```bash
   cd TRADER
   npm install
   ```

4. **Configuration**
   - Update `TRADER/python_backend/server.py` with your Kite API credentials
   - Update `TRADER/python_backend/server.py` with your OpenAI API key
   - Update `TRADER/src/config.js` with your backend URL

### Running the Application

1. **Start Backend Server**
   ```bash
   cd TRADER/python_backend
   python server.py
   ```

2. **Start Frontend Development Server**
   ```bash
   cd TRADER
   npm run dev
   ```

3. **Access the Application**
   - Open http://localhost:5173 in your browser
   - Login with password "1234"
   - Connect to Kite and start trading!

## 📱 Dashboard Features

### Overview Tab
- Portfolio summary with total value and P&L
- Recent activity tracking
- Performance metrics
- Quick stats and insights

### Holdings Tab
- Complete portfolio view
- Real-time P&L calculations
- Stock-wise performance analysis
- Sortable and filterable data

### Orders Tab
- Order history and management
- Real-time order status
- Order statistics
- Filter by status (open, complete, cancelled)

### Trades Tab
- Complete trade history
- Trade analytics and metrics
- Sortable columns
- Performance tracking

### Trading Tab
- Advanced order placement form
- Support for all order types (Market, Limit, SL, SL-M)
- Real-time quote fetching
- Order validation and confirmation

### Analytics Tab
- Detailed portfolio analysis
- Sector allocation charts
- Risk assessment
- Performance metrics
- Top holdings analysis

### Market Watch Tab
- Real-time market data
- Customizable watchlist
- Price alerts and monitoring
- Market summary and statistics

### AI Assistant Tab
- Advanced AI-powered chat
- Portfolio analysis tools
- Real-time data access
- Trading recommendations
- Risk assessment capabilities

## 🤖 AI Assistant Features

The AI assistant is powered by OpenAI's GPT-4 and has access to several tools:

- **Portfolio Summary Tool** - Analyzes your complete portfolio
- **Stock Quote Tool** - Fetches real-time market data
- **Risk Analysis Tool** - Assesses portfolio risk and diversification

### Sample AI Queries
- "What's my portfolio performance?"
- "Analyze my risk exposure"
- "Get me a quote for RELIANCE"
- "Should I diversify more?"
- "Which stocks are performing best?"

## 🔧 API Endpoints

### Authentication
- `POST /login` - User authentication
- `GET /kite-login-url` - Get Kite login URL
- `POST /kite-connect` - Connect to Kite with token

### Portfolio Data
- `GET /holdings` - Get portfolio holdings
- `GET /profile` - Get user profile
- `GET /margins` - Get account margins

### Trading
- `GET /orders` - Get order history
- `GET /trades` - Get trade history
- `POST /place-order` - Place new order

### Market Data
- `POST /quote` - Get real-time quotes
- `POST /historical-data` - Get historical data
- `GET /instruments/{exchange}` - Get instrument list

### AI Chat
- `POST /chat` - Enhanced AI chat with tools

## 🎨 UI/UX Features

- **Modern Design** - Clean, professional interface
- **Responsive Layout** - Works on all devices
- **Real-time Updates** - Live data refresh
- **Interactive Elements** - Hover effects and animations
- **Color-coded Data** - Easy-to-read profit/loss indicators
- **Intuitive Navigation** - Tab-based interface
- **Professional Charts** - Visual data representation

## 🔒 Security Notes

- API keys are embedded for demo (use environment variables in production)
- CORS is open for development (restrict in production)
- Password is hardcoded for demo purposes
- Implement proper authentication in production

## 🚀 Technologies Used

- **Backend:** FastAPI, KiteConnect, OpenAI, Pydantic, Uvicorn
- **Frontend:** React, React Router, Axios, Tailwind CSS
- **Build Tools:** Vite, ESLint
- **AI:** OpenAI GPT-4 with function calling

## 📈 Future Enhancements

- Real-time WebSocket data streaming
- Advanced charting with technical indicators
- Portfolio optimization suggestions
- Automated trading strategies
- Mobile app development
- Advanced risk management tools
