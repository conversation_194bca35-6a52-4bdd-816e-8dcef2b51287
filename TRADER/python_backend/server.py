import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from kiteconnect import KiteConnect
import instructor
from openai import OpenAI
import os
from typing import Optional, List
import json

# OpenAI setup
os.environ['OPENAI_API_KEY'] = "********************************************************************************************************************************************************************"
client = OpenAI()
client = instructor.patch(client)

# Kite API setup
kite_api_key = "eq5t4qqajzvgewyq"
kite_api_secret = "mqhu4z7mmemxtymd1dvuusiti8v8dpgb"

app = FastAPI()

origins = [
    "http://localhost",  # Covers cases where port is omitted
    "https://doq-module.netlify.app",
    "https://doqdevline.netlify.app",
    "https://a9b0-20-197-54-228.ngrok-free.app",  # Old ngrok URL
    "https://a554-152-59-26-232.ngrok-free.app"  # Current ngrok URL
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins, but filter manually
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)


@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    response = await call_next(request)
    origin = request.headers.get("origin")
    if origin and (origin.startswith("http://localhost") or origin in origins):
        response.headers["Access-Control-Allow-Origin"] = origin
    return response

# Global variables to store session data
kite_instance = None
user_holdings = None
access_token = None

# Pydantic models
class LoginRequest(BaseModel):
    password: str

class TokenRequest(BaseModel):
    request_token: str

class ChatRequest(BaseModel):
    message: str

class OrderRequest(BaseModel):
    tradingsymbol: str
    exchange: str
    transaction_type: str  # BUY or SELL
    order_type: str  # MARKET, LIMIT, SL, SL-M
    quantity: int
    price: Optional[float] = None
    trigger_price: Optional[float] = None
    product: str = "CNC"  # CNC, MIS, NRML

class HistoricalDataRequest(BaseModel):
    instrument_token: str
    from_date: str
    to_date: str
    interval: str = "day"  # minute, 3minute, 5minute, 10minute, 15minute, 30minute, 60minute, day

class QuoteRequest(BaseModel):
    instruments: List[str]

def ensure_kite_connection():
    """Ensure kite instance is properly initialized with access token."""
    global kite_instance, access_token
    if not kite_instance:
        raise HTTPException(status_code=400, detail="Kite not connected. Please connect to Kite first.")
    if not access_token:
        raise HTTPException(status_code=400, detail="No access token. Please reconnect to Kite.")

    # Ensure access token is set
    kite_instance.set_access_token(access_token)
    return kite_instance


@app.get("/")
async def root():
    """Root endpoint for testing server availability."""
    return {"status": "ok", "message": "Trading Chatbot Server is running"}

@app.get("/status")
async def get_status():
    """Get connection status."""
    global kite_instance, access_token, user_holdings
    return {
        "kite_connected": kite_instance is not None,
        "has_access_token": access_token is not None,
        "has_holdings": user_holdings is not None,
        "status": "connected" if (kite_instance and access_token) else "disconnected"
    }


@app.post("/login")
async def login(request: LoginRequest):
    """Authenticate user with hardcoded password."""
    if request.password == "1234":
        return {"success": True, "message": "Login successful"}
    else:
        raise HTTPException(status_code=401, detail="Invalid password")


@app.get("/kite-login-url")
async def get_kite_login_url():
    """Get Kite Connect login URL."""
    global kite_instance
    kite_instance = KiteConnect(api_key=kite_api_key)
    login_url = kite_instance.login_url()
    return {"login_url": login_url}


@app.get("/kite-token")
async def handle_kite_redirect(request: Request):
    """Handle Kite redirect and extract request token."""
    # This endpoint will be called by Kite after user login
    # The request token will be in the URL parameters
    request_token = request.query_params.get("request_token")
    status = request.query_params.get("status")

    if status == "success" and request_token:
        # Redirect to frontend with token
        return {"request_token": request_token, "status": "success"}
    else:
        raise HTTPException(
            status_code=400, detail="Kite login failed or was cancelled")


@app.post("/kite-connect")
async def connect_kite(request: TokenRequest):
    """Exchange request token for access token and fetch holdings."""
    global user_holdings, kite_instance, access_token

    if not kite_instance:
        kite_instance = KiteConnect(api_key=kite_api_key)

    try:
        # Generate session and set access token
        data = kite_instance.generate_session(
            request.request_token, api_secret=kite_api_secret)
        access_token = data["access_token"]
        kite_instance.set_access_token(access_token)

        # Fetch holdings
        user_holdings = kite_instance.positions()

        return {
            "success": True,
            "message": "Kite connected successfully",
            "holdings": user_holdings
        }
    except Exception as e:
        print(f"Kite connection error: {e}")
        raise HTTPException(
            status_code=400, detail=f"Failed to connect to Kite: {str(e)}")


@app.get("/holdings")
async def get_holdings():
    """Get current holdings."""
    global user_holdings

    if not user_holdings:
        raise HTTPException(
            status_code=400, detail="No holdings data available. Please connect to Kite first.")
    print(user_holdings)
    return {"holdings": user_holdings}


@app.get("/profile")
async def get_profile():
    """Get user profile information."""
    try:
        kite = ensure_kite_connection()
        profile = kite.profile()
        return {"profile": profile}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get profile: {str(e)}")

@app.get("/margins")
async def get_margins():
    """Get user margin information."""
    try:
        kite = ensure_kite_connection()
        margins = kite.margins()
        return {"margins": margins}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get margins: {str(e)}")

@app.get("/orders")
async def get_orders():
    """Get all orders."""
    try:
        kite = ensure_kite_connection()
        orders = kite.orders()
        return {"orders": orders}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get orders: {str(e)}")

@app.get("/trades")
async def get_trades():
    """Get all trades."""
    try:
        kite = ensure_kite_connection()
        trades = kite.trades()
        return {"trades": trades}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get trades: {str(e)}")

@app.post("/place-order")
async def place_order(order: OrderRequest):
    """Place a new order."""
    try:
        kite = ensure_kite_connection()

        order_params = {
            "tradingsymbol": order.tradingsymbol.upper(),
            "exchange": order.exchange.upper(),
            "transaction_type": order.transaction_type.upper(),
            "order_type": order.order_type.upper(),
            "quantity": order.quantity,
            "product": order.product.upper()
        }

        if order.price:
            order_params["price"] = order.price
        if order.trigger_price:
            order_params["trigger_price"] = order.trigger_price

        order_id = kite.place_order(**order_params)
        return {"success": True, "order_id": order_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to place order: {str(e)}")

@app.post("/quote")
async def get_quote(request: QuoteRequest):
    """Get market quotes for instruments."""
    try:
        kite = ensure_kite_connection()

        # Validate instruments format
        if not request.instruments:
            raise HTTPException(status_code=400, detail="No instruments provided")

        # Clean and validate instrument format
        valid_instruments = []
        for instrument in request.instruments:
            if not instrument or ':' not in instrument:
                continue

            # Ensure proper format: EXCHANGE:SYMBOL
            parts = instrument.split(':')
            if len(parts) == 2:
                exchange, symbol = parts
                if exchange.upper() in ['NSE', 'BSE', 'NFO', 'CDS', 'MCX'] and symbol.strip():
                    valid_instruments.append(f"{exchange.upper()}:{symbol.upper()}")

        if not valid_instruments:
            raise HTTPException(status_code=400, detail="No valid instruments found. Use format EXCHANGE:SYMBOL (e.g., NSE:RELIANCE)")

        print(f"Fetching quotes for: {valid_instruments}")
        quotes = kite.quote(valid_instruments)
        return {"quotes": quotes}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Quote error: {e}")
        error_msg = str(e)
        if "Invalid instrument" in error_msg:
            raise HTTPException(status_code=400, detail="Invalid instrument format. Use EXCHANGE:SYMBOL (e.g., NSE:RELIANCE)")
        elif "Token required" in error_msg or "Access token" in error_msg:
            raise HTTPException(status_code=401, detail="Authentication required. Please reconnect to Kite.")
        else:
            raise HTTPException(status_code=500, detail=f"Failed to get quotes: {error_msg}")

@app.post("/historical-data")
async def get_historical_data(request: HistoricalDataRequest):
    """Get historical data for an instrument."""
    try:
        kite = ensure_kite_connection()
        historical_data = kite.historical_data(
            instrument_token=request.instrument_token,
            from_date=request.from_date,
            to_date=request.to_date,
            interval=request.interval
        )
        return {"historical_data": historical_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get historical data: {str(e)}")

@app.get("/instruments/{exchange}")
async def get_instruments(exchange: str):
    """Get all instruments for an exchange."""
    try:
        kite = ensure_kite_connection()
        instruments = kite.instruments(exchange.upper())
        return {"instruments": instruments}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get instruments: {str(e)}")

@app.post("/chat")
async def chat_about_holdings(request: ChatRequest):
    """Enhanced AI chat with trading capabilities."""
    global user_holdings, kite_instance

    # Define tools that AI can use
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_portfolio_summary",
                "description": "Get a summary of the user's portfolio including total value, P&L, and top holdings",
                "parameters": {"type": "object", "properties": {}}
            }
        },
        {
            "type": "function",
            "function": {
                "name": "get_stock_quote",
                "description": "Get current market quote for a stock",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string", "description": "Stock symbol (e.g., RELIANCE, TCS)"}
                    },
                    "required": ["symbol"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "analyze_portfolio_risk",
                "description": "Analyze portfolio risk and diversification",
                "parameters": {"type": "object", "properties": {}}
            }
        }
    ]

    try:
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": """You're an expert financial advisor and trading assistant. You have access to the user's portfolio data and can help with:
                - Portfolio analysis and insights
                - Stock recommendations
                - Risk assessment
                - Market analysis
                - Trading strategies

                Use the available tools to get real-time data when needed. Be helpful, accurate, and provide actionable insights."""},
                {"role": "user", "content": f"My current holdings: {user_holdings}. Question: {request.message}"}
            ],
            tools=tools,
            tool_choice="auto"
        )

        # Handle tool calls if any
        if response.choices[0].message.tool_calls:
            # Process tool calls and get results
            tool_results = []
            for tool_call in response.choices[0].message.tool_calls:
                if tool_call.function.name == "get_portfolio_summary":
                    result = get_portfolio_summary_data()
                    tool_results.append(result)
                elif tool_call.function.name == "get_stock_quote":
                    args = json.loads(tool_call.function.arguments)
                    result = get_stock_quote_data(args["symbol"])
                    tool_results.append(result)
                elif tool_call.function.name == "analyze_portfolio_risk":
                    result = analyze_portfolio_risk_data()
                    tool_results.append(result)

            # Create follow-up response with tool results
            follow_up_response = client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You're an expert financial advisor. Use the tool results to provide comprehensive insights."},
                    {"role": "user", "content": request.message},
                    {"role": "assistant", "content": response.choices[0].message.content or ""},
                    {"role": "user", "content": f"Tool results: {tool_results}"}
                ]
            )
            return {"response": follow_up_response.choices[0].message.content.strip()}

        return {"response": response.choices[0].message.content.strip()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get AI response: {str(e)}")

def get_portfolio_summary_data():
    """Helper function to get portfolio summary."""
    global user_holdings
    if not user_holdings:
        return "No portfolio data available"

    try:
        # Calculate portfolio metrics
        total_value = 0
        total_pnl = 0
        holdings_list = []

        if isinstance(user_holdings, dict) and 'net' in user_holdings:
            holdings_data = user_holdings['net']
        else:
            holdings_data = user_holdings

        for holding in holdings_data:
            if isinstance(holding, dict):
                quantity = holding.get('quantity', 0)
                ltp = holding.get('last_price', 0)
                avg_price = holding.get('average_price', 0)

                current_value = quantity * ltp
                invested_value = quantity * avg_price
                pnl = current_value - invested_value

                total_value += current_value
                total_pnl += pnl

                holdings_list.append({
                    'symbol': holding.get('tradingsymbol', 'Unknown'),
                    'quantity': quantity,
                    'current_value': current_value,
                    'pnl': pnl
                })

        return {
            'total_value': total_value,
            'total_pnl': total_pnl,
            'pnl_percentage': (total_pnl / (total_value - total_pnl)) * 100 if total_value > total_pnl else 0,
            'holdings_count': len(holdings_list),
            'top_holdings': sorted(holdings_list, key=lambda x: x['current_value'], reverse=True)[:5]
        }
    except Exception as e:
        return f"Error calculating portfolio summary: {str(e)}"

def get_stock_quote_data(symbol):
    """Helper function to get stock quote."""
    try:
        kite = ensure_kite_connection()

        # Clean symbol and try different exchange combinations
        clean_symbol = symbol.upper().strip()
        instruments = [f"NSE:{clean_symbol}", f"BSE:{clean_symbol}"]

        quotes = kite.quote(instruments)
        return quotes
    except Exception as e:
        return f"Error getting quote for {symbol}: {str(e)}"

def analyze_portfolio_risk_data():
    """Helper function to analyze portfolio risk."""
    global user_holdings
    if not user_holdings:
        return "No portfolio data available"

    try:
        # Basic risk analysis
        holdings_data = user_holdings.get('net', user_holdings) if isinstance(user_holdings, dict) else user_holdings

        sectors = {}
        total_value = 0

        for holding in holdings_data:
            if isinstance(holding, dict):
                symbol = holding.get('tradingsymbol', '')
                quantity = holding.get('quantity', 0)
                ltp = holding.get('last_price', 0)
                value = quantity * ltp
                total_value += value

                # Simple sector classification (this could be enhanced with actual sector data)
                if any(bank in symbol.upper() for bank in ['BANK', 'HDFC', 'ICICI', 'SBI', 'AXIS']):
                    sectors['Banking'] = sectors.get('Banking', 0) + value
                elif any(it in symbol.upper() for it in ['TCS', 'INFY', 'WIPRO', 'TECH']):
                    sectors['IT'] = sectors.get('IT', 0) + value
                else:
                    sectors['Others'] = sectors.get('Others', 0) + value

        # Calculate concentration risk
        sector_percentages = {sector: (value/total_value)*100 for sector, value in sectors.items()}
        max_concentration = max(sector_percentages.values()) if sector_percentages else 0

        risk_level = "Low" if max_concentration < 30 else "Medium" if max_concentration < 50 else "High"

        return {
            'risk_level': risk_level,
            'sector_allocation': sector_percentages,
            'max_sector_concentration': max_concentration,
            'diversification_score': 100 - max_concentration
        }
    except Exception as e:
        return f"Error analyzing portfolio risk: {str(e)}"


def start():
    uvicorn.run("server:app", host="0.0.0.0", port=8000, reload=True)


if __name__ == "__main__":
    start()
