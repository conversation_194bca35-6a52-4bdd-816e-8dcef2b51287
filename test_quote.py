#!/usr/bin/env python3
"""
Test script to debug quote endpoint issues
"""

import requests
import json

API_URL = "http://localhost:8000"

def test_server_status():
    """Test if server is running"""
    try:
        response = requests.get(f"{API_URL}/")
        print(f"✅ Server Status: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Server not running: {e}")
        return False

def test_connection_status():
    """Test connection status"""
    try:
        response = requests.get(f"{API_URL}/status")
        status = response.json()
        print(f"📊 Connection Status: {status}")
        return status.get("status") == "connected"
    except Exception as e:
        print(f"❌ Failed to get status: {e}")
        return False

def test_quote_endpoint():
    """Test quote endpoint with various instruments"""
    test_instruments = [
        ["NSE:RELIANCE"],
        ["NSE:TCS"],
        ["NSE:INFY", "NSE:WIPRO"],
        ["BSE:RELIANCE"],
        ["INVALID:SYMBOL"],  # This should fail
        ["NSE:NONEXISTENT"]  # This might fail
    ]
    
    for instruments in test_instruments:
        try:
            print(f"\n🔍 Testing instruments: {instruments}")
            response = requests.post(f"{API_URL}/quote", json={
                "instruments": instruments
            })
            
            if response.status_code == 200:
                quotes = response.json().get("quotes", {})
                print(f"✅ Success: Got {len(quotes)} quotes")
                for instrument, quote in quotes.items():
                    if quote:
                        print(f"   {instrument}: ₹{quote.get('last_price', 'N/A')}")
                    else:
                        print(f"   {instrument}: No data")
            else:
                print(f"❌ Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def main():
    print("🧪 Quote Endpoint Test Script")
    print("=" * 40)
    
    # Test server
    if not test_server_status():
        return
    
    # Test connection
    if not test_connection_status():
        print("⚠️  Kite not connected. Please connect through the web interface first.")
        print("   1. Go to http://localhost:5173")
        print("   2. Login with password '1234'")
        print("   3. Connect to Kite")
        print("   4. Run this test again")
        return
    
    # Test quotes
    test_quote_endpoint()
    
    print("\n✅ Test completed!")

if __name__ == "__main__":
    main()
